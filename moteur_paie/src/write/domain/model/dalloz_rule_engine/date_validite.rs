use crate::dalloz_structure::DateValidite;
use crate::write::domain::model::dalloz_rule_engine::rule::Rule;
use chrono::NaiveDate;

impl Rule for Option<DateValidite> {
    type InputType = NaiveDate;

    fn is_applicable(&self, item: &Self::InputType) -> bool {
        if self.is_none() {
            return true;
        }

        (self.as_ref().unwrap().min.is_none()
            || self.as_ref().unwrap().min.as_ref().unwrap() <= item)
            && (self.as_ref().unwrap().max.is_none()
                || self.as_ref().unwrap().max.as_ref().unwrap() >= item)
    }
}

#[cfg(test)]
mod tests {
    use super::Rule;
    use crate::dalloz_structure::DateValidite;
    use chrono::NaiveDate;

    #[test]
    fn it_should_validate_date_in_range() {
        // Given I have a DateValidite
        let date_validite = Some(DateValidite {
            min: Some(NaiveDate::from_ymd_opt(2020, 1, 1).unwrap()),
            max: Some(NaiveDate::from_ymd_opt(2020, 12, 31).unwrap()),
        });

        // When I check if a date is in range
        let result = date_validite.is_applicable(&NaiveDate::from_ymd_opt(2020, 6, 1).unwrap());

        assert_eq!(result, true);
    }

    #[test]
    fn it_should_return_true_when_borne_min_is_none_and_date_is_in_range() {
        // Given I have a DateValidite
        let date_validite = Some(DateValidite {
            min: None,
            max: Some(NaiveDate::from_ymd_opt(2020, 12, 31).unwrap()),
        });

        // When I check if a date is in range
        let result = date_validite.is_applicable(&NaiveDate::from_ymd_opt(2020, 12, 31).unwrap());

        assert_eq!(result, true);
    }

    #[test]
    fn it_should_return_true_when_borne_max_is_none_and_date_is_in_range() {
        // Given I have a DateValidite
        let date_validite = Some(DateValidite {
            min: Some(NaiveDate::from_ymd_opt(2020, 12, 1).unwrap()),
            max: None,
        });

        // When I check if a date is in range
        let result =
            date_validite.is_applicable(&NaiveDate::from_ymd_opt(2020, 12, 1).unwrap());

        assert_eq!(result, true);
    }
}
