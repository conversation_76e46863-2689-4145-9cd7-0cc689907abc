
pub enum UniteMesureType {
    HeuresDeVol,
    Annee,
    Interventions,
    Mois,
    LongueurDuBateauEnMetre,
    Semaine,
    LongueurDuConvoiEnMetre,
    JoursCalendaires,
    HeuresDeNuit,
    HoraireHebdomadaire,
    HeuresDeJour,
    HoraireMensuel,
    HoraireJournalier,
    HoraireAnnuel,
    Kilometres,
    Astreinte,
    Exemplaires,
    Periode,
    JoursSupplementaires,
    JoursOuvres,
    JoursOuvrables,
    Contrat,
    KWH,
    AvenantComplementHeures,
    MetresCarres,
    DimancheTravaille,
    JoursNonOuvrables,
    JoursDeTravailParAns,
    JoursConsecutifs,
    HauteurEnMetre,
    Heures,
    Salaries,
    Minutes,
    JoursDeTravailDansLeMois,
    ProfondeurEnMetre,
    HeuresAvenant,
    ProfondeurEnCentimetre,
    HeuresDeTravailDansLeFroidOuLeChaud,
    Croquis,
    Pourcentage,
    SeanceEnregistrement,
    Representations,
    ExamenMedicalObligatoire,
    Places,
    DemiJournee,
    Musiciens,
    JoursFrancs,
    PlafondMensuelDeLaSS,
    PlafondJournalierDeLeSS,
    ETP,
    HeuresDeTravailDansAnnee,
    JoursDeTravailParSemaine,
    MinutesInterpretationEnregistreesEtUtilisees,
    Eleves,
    Internes,
    HeuresDeFormation,
    JoursDeTravailSurUneSuiteDe7JoursConsecutifs,
    Trimestre,
    EffectifPondere,
    Classes,
}

pub trait UniteMesureTrait {
    fn get_periodicite(&self) -> UniteMesureType;
}
