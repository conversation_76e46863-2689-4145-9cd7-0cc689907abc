use crate::dalloz_types::{ItemExclus, SituationFamiliale};
use crate::write::domain::model::dalloz_rule_engine::rule::Rule;

impl Rule for Vec<ItemExclus<SituationFamiliale>> {
    type InputType = Option<String>;

    fn is_applicable(&self, item: Self::InputType) -> bool {
        if self.is_empty() {
            return true;
        }

        if item.is_none() {
            return false;
        }

        let item_value = item.as_ref().unwrap();
        
        // Check if there are any inclusion items (exclu = false)
        let inclusion_items: Vec<_> = self.iter()
            .filter(|item| !item.0.exclu.unwrap_or(false))
            .collect();
        
        // Check if there are any exclusion items (exclu = true)
        let exclusion_items: Vec<_> = self.iter()
            .filter(|item| item.0.exclu.unwrap_or(false))
            .collect();

        // If there are exclusion items and the item matches any of them, exclude it
        if !exclusion_items.is_empty() {
            if exclusion_items.iter().any(|excl_item| &excl_item.0.libelle == item_value) {
                return false;
            }
        }

        // If there are inclusion items, the item must match one of them
        if !inclusion_items.is_empty() {
            return inclusion_items.iter().any(|incl_item| &incl_item.0.libelle == item_value);
        }

        // If there are only exclusion items and the item doesn't match any, it's applicable
        true
    }
}

#[cfg(test)]
mod tests {
    use crate::dalloz_structure::ItemExclus;
    use crate::dalloz_types::SituationFamiliale;
    use super::Rule;

    #[test]
    fn it_should_return_true_for_empty_list() {
        // Given I have an empty list
        let list = Vec::new();

        // When I check if an item is applicable
        let result = list.is_applicable(None);

        assert_eq!(result, true);
    }

    #[test]
    fn it_should_return_false_for_none_item() {
        // Given I have a list
        let list = vec![ItemExclus(SituationFamiliale {
            code: "01".to_string(),
            libelle: "Test SituationFamiliale".to_string(),
            exclu: Some(false),
        })];

        // When I check if an item is applicable
        let result = list.is_applicable(None);

        assert_eq!(result, false);
    }

    #[test]
    fn it_should_return_true_when_inclusion_item_matches() {
        // Given I have a list with inclusion items
        let list = vec![ItemExclus(SituationFamiliale {
            code: "01".to_string(),
            libelle: "Test SituationFamiliale".to_string(),
            exclu: Some(false), // Inclusion item
        })];

        // When I check if an item is applicable
        let result = list.is_applicable(Some("Test SituationFamiliale".to_string()));

        assert_eq!(result, true);
    }

    #[test]
    fn it_should_return_false_when_exclusion_item_matches() {
        // Given I have a list with exclusion items
        let list = vec![ItemExclus(SituationFamiliale {
            code: "01".to_string(),
            libelle: "Test SituationFamiliale".to_string(),
            exclu: Some(true), // Exclusion item
        })];

        // When I check if an item is applicable
        let result = list.is_applicable(Some("Test SituationFamiliale".to_string()));

        assert_eq!(result, false);
    }

    #[test]
    fn it_should_return_true_when_not_in_exclusion_list() {
        // Given I have a list with exclusion items
        let list = vec![ItemExclus(SituationFamiliale {
            code: "01".to_string(),
            libelle: "Test SituationFamiliale".to_string(),
            exclu: Some(true), // Exclusion item
        })];

        // When I check if an item is applicable
        let result = list.is_applicable(Some("Other SituationFamiliale".to_string()));

        assert_eq!(result, true);
    }

    #[test]
    fn it_should_handle_mixed_inclusion_and_exclusion_items() {
        // Given I have a list with both inclusion and exclusion items
        let list = vec![
            ItemExclus(SituationFamiliale {
                code: "01".to_string(),
                libelle: "Included SituationFamiliale".to_string(),
                exclu: Some(false), // Inclusion item
            }),
            ItemExclus(SituationFamiliale {
                code: "02".to_string(),
                libelle: "Excluded SituationFamiliale".to_string(),
                exclu: Some(true), // Exclusion item
            }),
        ];

        // When I check if inclusion item is applicable
        let result1 = list.is_applicable(Some("Included SituationFamiliale".to_string()));
        assert_eq!(result1, true);

        // When I check if exclusion item is applicable
        let result2 = list.is_applicable(Some("Excluded SituationFamiliale".to_string()));
        assert_eq!(result2, false);

        // When I check if other item is applicable
        let result3 = list.is_applicable(Some("Other SituationFamiliale".to_string()));
        assert_eq!(result3, false); // Not in inclusion list
    }

    #[test]
    fn it_should_use_the_exclu_field_and_return_true_when_its_not_set() {
        // Given I have a list
        let list = vec![ItemExclus(SituationFamiliale {
            code: "01".to_string(),
            libelle: "Test SituationFamiliale".to_string(),
            exclu: None, // When None, it is not excluded (treated as inclusion)
        })];

        // When I check if an item is applicable
        let result = list.is_applicable(Some("Test SituationFamiliale".to_string()));

        assert_eq!(result, true);
    }
}
