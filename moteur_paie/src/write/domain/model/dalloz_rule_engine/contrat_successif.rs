use crate::dalloz_types::{ItemInclus, ContratSuccessif};
use crate::write::domain::model::dalloz_rule_engine::rule::Rule;

impl Rule for Vec<ItemInclus<ContratSuccessif>> {
    type InputType = Option<String>;

    fn is_applicable(&self, item: Self::InputType) -> bool {
        if self.is_empty() {
            return true;
        }

        if item.is_none() {
            return false;
        }

        self.iter()
            .filter(|item| !item.0.exclu.unwrap_or(false))
            .any(|contrat_successif| &contrat_successif.0.libelle == item.as_ref().unwrap())
    }
}

#[cfg(test)]
mod tests {
    use crate::dalloz_structure::ItemInclus;
    use crate::dalloz_types::ContratSuccessif;
    use super::Rule;

    #[test]
    fn it_should_return_true_for_empty_list() {
        // Given I have an empty list
        let list = Vec::new();

        // When I check if an item is applicable
        let result = list.is_applicable(None);

        assert_eq!(result, true);
    }

    #[test]
    fn it_should_return_false_for_none_item() {
        // Given I have a list
        let list = vec![ItemInclus(ContratSuccessif {
            code: "01".to_string(),
            libelle: "Test ContratSuccessif".to_string(),
            exclu: Some(false),
        })];

        // When I check if an item is applicable
        let result = list.is_applicable(None);

        assert_eq!(result, false);
    }

    #[test]
    fn it_should_use_the_exclu_field_and_return_true_when_its_not_set() {
        // Given I have a list
        let list = vec![ItemInclus(ContratSuccessif {
            code: "01".to_string(),
            libelle: "Test ContratSuccessif".to_string(),
            exclu: None, // When None, it is not excluded
        })];

        // When I check if an item is applicable
        let result = list.is_applicable(Some("Test ContratSuccessif".to_string()));

        assert_eq!(result, true);
    }

    #[test]
    fn it_should_use_the_exclu_field_and_return_false() {
        // Given I have a list
        let list = vec![ItemInclus(ContratSuccessif {
            code: "01".to_string(),
            libelle: "Test ContratSuccessif".to_string(),
            exclu: Some(true), // When true, it should be excluded
        })];

        // When I check if an item is applicable
        let result = list.is_applicable(Some("Test ContratSuccessif".to_string()));

        assert_eq!(result, false);
    }

    #[test]
    fn it_should_return_true_when_matching_item_found() {
        // Given I have a list with multiple items
        let list = vec![
            ItemInclus(ContratSuccessif {
                code: "01".to_string(),
                libelle: "Test ContratSuccessif 1".to_string(),
                exclu: Some(false),
            }),
            ItemInclus(ContratSuccessif {
                code: "02".to_string(),
                libelle: "Test ContratSuccessif 2".to_string(),
                exclu: Some(false),
            }),
        ];

        // When I check if an item is applicable
        let result = list.is_applicable(Some("Test ContratSuccessif 2".to_string()));

        assert_eq!(result, true);
    }

    #[test]
    fn it_should_return_false_when_no_matching_item_found() {
        // Given I have a list
        let list = vec![ItemInclus(ContratSuccessif {
            code: "01".to_string(),
            libelle: "Test ContratSuccessif".to_string(),
            exclu: Some(false),
        })];

        // When I check if an item is applicable
        let result = list.is_applicable(Some("Other ContratSuccessif".to_string()));

        assert_eq!(result, false);
    }
}
