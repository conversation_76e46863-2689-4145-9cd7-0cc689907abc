use crate::app_errors::AppError;
use crate::app_errors::AppError::ItemNotFound;
use crate::dalloz_structure::{
    ItemExclus, TypeAbsenceSante, VariableClientAbsence, ZonesGeographiques,
};
use crate::read::domain::model::convention_collective::convention::ConventionCollective;
use crate::read::domain::query::convention_collective::get_convention_collective_query::DynGetConventionCollectiveQuery;
use crate::write::domain::model::dalloz_rule_engine::rule::Rule;
use chrono::NaiveDate;
use std::collections::HashMap;

type TauxMaitien = HashMap<String, f64>;

pub async fn find_carence<T: ConventionCollective>(
    id_cc: String,
    type_absence: String,
    date: NaiveDate,
    zone_geographique: String,
    categorie_professionnelle: String,
    organisme_signataire: Option<String>,
    emploi: String,
    secteur_activite: String,
    variable_absence_client: Option<String>,
    convention_collective_query: DynGetConventionCollectiveQuery<T>,
) -> Result<TauxMaitien, AppError> {
    let convention_collective = convention_collective_query
        .get(id_cc.parse().unwrap())
        .await?;

    let is_signataire = organisme_signataire.is_some();

    for carence in convention_collective.get_carences() {
        let input_rule = &carence.carence_input;

        if !input_rule.date_validite.is_applicable(&date) {
            println!("Date validite non applicable for rule id {}", &carence.id);
            continue;
        }

        if !input_rule.type_absence_sante.is_applicable(&type_absence) {
            println!("Type absence sante non applicable for rule id {}", &carence.id);
            continue;
        }

        if !input_rule
            .variable_client_absence
            .is_applicable(&variable_absence_client)
        {
            println!("Variable client absence non applicable for rule id {}", &carence.id);
            continue;
        }

        if !input_rule
            .zones_geographiques
            .is_applicable(&Some(zone_geographique.clone()))
        {
            println!("Zone geographique non applicable for rule id {}", &carence.id);
            continue;
        }

        if input_rule
            .categories_professionnelles
            .is_applicable(&Some(categorie_professionnelle.clone()))
        {
            continue;
        }

        if input_rule.adherent.is_some()
            && input_rule.adherent.as_ref().unwrap().libelle == "Oui"
            && !is_signataire
        {
            continue;
        }

        if !input_rule.emploi.is_applicable(&Some(emploi.clone())) {
            
            continue;
        }

        let secteurs_exclus = input_rule
            .secteurs_activite
            .iter()
            .filter(|item| item.0.exclu.unwrap())
            .any(|item| item.0.libelle == secteur_activite);

        let secteurs_inclus = input_rule
            .secteurs_activite
            .iter()
            .filter(|item| !item.0.exclu.unwrap())
            .any(|item| item.0.libelle == secteur_activite);

        if secteurs_exclus && !secteurs_inclus {
            continue;
        }

        let output = &carence.carence_output;
        let mut result = TauxMaitien::new();

        if let Some(delai_carence) = &output.droits_carence {
            result.insert(
                "delai_carence".to_string(),
                delai_carence.nb_jours.unwrap_or_default(),
            );
        }

        return Ok(result);
    }

    Err(ItemNotFound("carence not found".to_string()))
}

mod test {
    use chrono::NaiveDate;
    use crate::read::adapters::secondary::in_memory::convention_collective::convention_collectives_query_in_memory::get_convention_collective_in_memory;
    use crate::read::domain::use_cases::regles_maladie::get_carence::find_carence;

    #[tokio::test]
    async fn test_find_carence() {
        // Given
        let id_cc = "9999".to_string();
        let type_absence = "maladie".to_string();
        let date = NaiveDate::from_ymd_opt(2025, 01, 01).unwrap();
        let convention_query =
            get_convention_collective_in_memory("resources/cc_9999_droit_commun.json");
        let zone_geographique = "Loire Atlanique".to_string();
        let categorie_professionnelle = "Agent de Maitrise".to_string();
        let emploi = "Chauffeur routier".to_string();
        let secteur_activite =
            "Transport routier de marchandises et activités auxiliaires".to_string();

        // When
        let result = find_carence(
            id_cc,
            type_absence,
            date,
            zone_geographique,
            categorie_professionnelle,
            None,
            emploi,
            secteur_activite,
            None,
            convention_query,
        )
        .await;

        // Then
        let carence = result.unwrap();
        assert_eq!(carence.get("delai_carence").unwrap(), &7.0);
    }

    #[tokio::test]
    async fn test_find_carence_for_zone_geographique() {
        // Given
        let id_cc = "9999".to_string();
        let type_absence = "maladie".to_string();
        let date = NaiveDate::from_ymd_opt(2025, 01, 01).unwrap();
        let convention_query =
            get_convention_collective_in_memory("resources/cc_9999_droit_commun.json");
        let zone_geographique = "Moselle".to_string();
        let categorie_professionnelle = "Agent de Maitrise".to_string();
        let emploi = "Chauffeur routier".to_string();
        let secteur_activite =
            "Transport routier de marchandises et activités auxiliaires".to_string();

        // When
        let result = find_carence(
            id_cc,
            type_absence,
            date,
            zone_geographique,
            categorie_professionnelle,
            None,
            emploi,
            secteur_activite,
            None,
            convention_query,
        )
        .await;

        // Then
        assert!(result.is_ok());
        let carence = result.unwrap();
        assert_eq!(carence.get("delai_carence").unwrap(), &0.0);
    }

    #[tokio::test]
    async fn test_find_carence_for_emploi() {
        // Given
        let id_cc = "16".to_string();
        let type_absence = "maladie".to_string();
        let date = NaiveDate::from_ymd_opt(2025, 01, 01).unwrap();
        let convention_query =
            get_convention_collective_in_memory("resources/cc_16_transport_routier.json");
        let zone_geographique = "Moselle".to_string();
        let categorie_professionnelle = "Agent de Maitrise".to_string();
        let emploi = "Chauffeur routier".to_string();
        let secteur_activite =
            "Transport routier de marchandises et activités auxiliaires".to_string();

        // When
        let result = find_carence(
            id_cc,
            type_absence,
            date,
            zone_geographique,
            categorie_professionnelle,
            None,
            emploi,
            secteur_activite,
            None,
            convention_query,
        )
        .await;

        // Then
        assert!(result.is_ok());
        let carence = result.unwrap();
        assert_eq!(carence.get("delai_carence").unwrap(), &0.0);
    }

    #[tokio::test]
    async fn test_find_carence_for_secteur_activite() {
        // Given
        let id_cc = "1486".to_string();
        let type_absence = "maladie".to_string();
        let date = NaiveDate::from_ymd_opt(2025, 01, 01).unwrap();
        let convention_query = get_convention_collective_in_memory("resources/cc_1486_syntec.json");
        let zone_geographique = "Moselle".to_string();
        let categorie_professionnelle = "Agent de Maitrise".to_string();
        let emploi = "Chauffeur routier".to_string();
        let secteur_activite =
            "Transport routier de marchandises et activités auxiliaires".to_string();

        // When
        let result = find_carence(
            id_cc,
            type_absence,
            date,
            zone_geographique,
            categorie_professionnelle,
            None,
            emploi,
            secteur_activite,
            None,
            convention_query,
        )
        .await;

        // Then
        assert!(result.is_ok());
        let carence = result.unwrap();
        assert_eq!(carence.get("delai_carence").unwrap(), &0.0);
    }

    #[tokio::test]
    async fn test_find_carence_for_adherent() {
        // Given
        let id_cc = "16".to_string();
        let type_absence = "maladie".to_string();
        let date = NaiveDate::from_ymd_opt(2025, 01, 01).unwrap();
        let convention_query =
            get_convention_collective_in_memory("resources/cc_16_transport_routier.json");
        let zone_geographique = "Moselle".to_string();
        let categorie_professionnelle = "Agent de Maitrise".to_string();
        let organisme_signataire = Some("Fédération syntec".to_string());
        let emploi = "Chauffeur routier".to_string();
        let secteur_activite =
            "Transport routier de marchandises et activités auxiliaires".to_string();

        // When
        let result = find_carence(
            id_cc,
            type_absence,
            date,
            zone_geographique,
            categorie_professionnelle,
            organisme_signataire,
            emploi,
            secteur_activite,
            None,
            convention_query,
        )
        .await;

        // Then
        assert!(result.is_ok());
        let carence = result.unwrap();
        assert_eq!(carence.get("delai_carence").unwrap(), &0.0);
    }
}
